<!DOCTYPE html>
<html lang="fa" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>BitVoucher API Documentation</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        @font-face {
            font-family: 'IRANSans';
            src: url('irsans.ttf') format('truetype');
            font-weight: normal;
            font-style: normal;
            font-display: swap;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'IRANSans', 'Tahoma', 'Arial', sans-serif;
            background: linear-gradient(135deg, #0c0c0c 0%, #1a1a2e 50%, #16213e 100%);
            color: #e0e6ed;
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            border: 1px solid rgba(255, 255, 255, 0.1);
            padding: 40px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
        }

        .header {
            text-align: center;
            margin-bottom: 40px;
        }

        .header h1 {
            font-size: 2.5rem;
            background: linear-gradient(45deg, #00d4ff, #5a67d8, #667eea);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            margin-bottom: 10px;
        }

        .header p {
            color: #a0aec0;
            font-size: 1.1rem;
        }

        .api-section {
            margin-bottom: 40px;
            background: rgba(255, 255, 255, 0.03);
            border-radius: 15px;
            padding: 30px;
            border: 1px solid rgba(255, 255, 255, 0.08);
        }

        .api-title {
            font-size: 1.5rem;
            color: #00d4ff;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .api-url {
            background: rgba(0, 212, 255, 0.1);
            border: 1px solid rgba(0, 212, 255, 0.3);
            border-radius: 10px;
            padding: 15px;
            margin: 15px 0;
            font-family: 'Courier New', monospace;
            color: #00d4ff;
            word-break: break-all;
            position: relative;
        }

        .copy-btn {
            position: absolute;
            top: 10px;
            left: 10px;
            background: rgba(0, 212, 255, 0.2);
            border: none;
            color: #00d4ff;
            padding: 5px 10px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 12px;
            font-family: 'IRANSans', 'Tahoma', 'Arial', sans-serif;
            transition: all 0.3s ease;
        }

        .copy-btn:hover {
            background: rgba(0, 212, 255, 0.4);
        }

        .response-container {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }

        .response-box {
            background: rgba(0, 0, 0, 0.3);
            border-radius: 12px;
            padding: 20px;
            border: 1px solid rgba(255, 255, 255, 0.1);
            transition: all 0.3s ease;
        }

        .response-box:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 30px rgba(0, 212, 255, 0.1);
            border-color: rgba(0, 212, 255, 0.3);
        }

        .response-title {
            font-size: 1.1rem;
            margin-bottom: 10px;
            padding: 8px 12px;
            border-radius: 8px;
            font-weight: bold;
        }

        .success {
            background: rgba(72, 187, 120, 0.2);
            color: #68d391;
            border: 1px solid rgba(72, 187, 120, 0.3);
        }

        .error {
            background: rgba(245, 101, 101, 0.2);
            color: #fc8181;
            border: 1px solid rgba(245, 101, 101, 0.3);
        }

        .used {
            background: rgba(237, 137, 54, 0.2);
            color: #ed8936;
            border: 1px solid rgba(237, 137, 54, 0.3);
        }

        .json-code {
            background: rgba(0, 0, 0, 0.5);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 8px;
            padding: 15px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            line-height: 1.6;
            overflow-x: auto;
            white-space: pre;
        }

        .json-key {
            color: #79b8ff;
        }

        .json-string {
            color: #85e89d;
        }

        .json-number {
            color: #f97583;
        }

        .json-boolean {
            color: #ffab70;
        }

        .description {
            margin-top: 15px;
            padding: 15px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 8px;
            border-right: 4px solid #00d4ff;
            color: #cbd5e0;
            line-height: 1.6;
        }

        .footer {
            text-align: center;
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid rgba(255, 255, 255, 0.1);
            color: #718096;
        }

        @media (max-width: 768px) {
            .container {
                padding: 20px;
            }
            
            .header h1 {
                font-size: 2rem;
            }
            
            .response-container {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1><i class="fas fa-book"></i> مستندات فنی</h1>
            <p>مستندات کامل API برای کار با کدهای ووچر BitVoucher</p>
        </div>



        <div class="api-section">
            <div class="api-title">
                <i class="fas fa-search"></i> نحوه گرفتن اطلاعات کد ووچر
            </div>
            <div class="api-url">
                <button class="copy-btn" onclick="copyToClipboard(this)">کپی</button>
                https://speedx-team.ir/BIT/api?token=TOKEN&code=CODE
            </div>
            <div class="description">
                <strong>پارامترها:</strong><br>
                • <code>token</code>: توکن شما از بخش "👀 مشاهده توکن"<br>
                • <code>code</code>: کد ووچر مورد نظر (مثال: BV-1A7D-7EAE-F261-24DD)
            </div>

            <div class="response-container">
                <div class="response-box">
                    <div class="response-title success"><i class="fas fa-check-circle"></i> کد ووچر معتبر و فعال</div>
                    <div class="json-code">{
  <span class="json-key">"status"</span>: <span class="json-string">"active"</span>,
  <span class="json-key">"is_used"</span>: <span class="json-boolean">false</span>,
  <span class="json-key">"voucher_code"</span>: <span class="json-string">"BV-1A7D-7EAE-F261-24DD"</span>,
  <span class="json-key">"amount_toman"</span>: <span class="json-number">20000</span>,
  <span class="json-key">"creator_user_id"</span>: <span class="json-string">"2038958340"</span>
}</div>
                    <div class="description">
                        این پاسخ زمانی برگردانده می‌شود که کد ووچر معتبر و هنوز استفاده نشده باشد.
                    </div>
                </div>

                <div class="response-box">
                    <div class="response-title used">⚠️ کد ووچر استفاده شده</div>
                    <div class="json-code">{
  <span class="json-key">"status"</span>: <span class="json-string">"used"</span>,
  <span class="json-key">"is_used"</span>: <span class="json-boolean">true</span>,
  <span class="json-key">"voucher_code"</span>: <span class="json-string">"BV-1A7D-7EAE-F261-24DD"</span>,
  <span class="json-key">"amount_toman"</span>: <span class="json-number">20000</span>,
  <span class="json-key">"creator_user_id"</span>: <span class="json-string">"2038958340"</span>
}</div>
                    <div class="description">
                        این پاسخ زمانی برگردانده می‌شود که کد ووچر معتبر است اما قبلاً استفاده شده است.
                    </div>
                </div>

                <div class="response-box">
                    <div class="response-title error">❌ کد ووچر نامعتبر</div>
                    <div class="json-code">{
  <span class="json-key">"status"</span>: <span class="json-string">"error"</span>,
  <span class="json-key">"message"</span>: <span class="json-string">"کد ووچر یافت نشد"</span>
}</div>
                    <div class="description">
                        این پاسخ زمانی برگردانده می‌شود که کد ووچر وارد شده در سیستم وجود ندارد.
                    </div>
                </div>
            </div>
        </div>

        <div class="api-section">
            <div class="api-title">
                <i class="fas fa-exchange-alt"></i> نحوه انتقال کد ووچر به حساب شما
            </div>
            <div class="api-url">
                <button class="copy-btn" onclick="copyToClipboard(this)">کپی</button>
                https://speedx-team.ir/BIT/api?token=TOKEN&bv-code=CODE&action=transmission
            </div>
            <div class="description">
                <strong>پارامترها:</strong><br>
                • <code>token</code>: توکن شما از بخش "👀 مشاهده توکن"<br>
                • <code>bv-code</code>: کد ووچر مورد نظر<br>
                • <code>action</code>: transmission (انتقال به حساب)
            </div>

            <div class="response-container">
                <div class="response-box">
                    <div class="response-title success">✅ انتقال موفق به حساب</div>
                    <div class="json-code">{
  <span class="json-key">"status"</span>: <span class="json-string">"success"</span>,
  <span class="json-key">"message"</span>: <span class="json-string">"کد ووچر با موفقیت به حساب شما منتقل شد"</span>,
  <span class="json-key">"amount"</span>: <span class="json-number">20000</span>,
  <span class="json-key">"new_balance"</span>: <span class="json-number">45000</span>
}</div>
                    <div class="description">
                        کد ووچر با موفقیت به حساب شما منتقل شد و موجودی جدید نمایش داده شده است.
                    </div>
                </div>

                <div class="response-box">
                    <div class="response-title error">❌ خطا در انتقال</div>
                    <div class="json-code">{
  <span class="json-key">"status"</span>: <span class="json-string">"error"</span>,
  <span class="json-key">"message"</span>: <span class="json-string">"کد ووچر نامعتبر یا استفاده شده است"</span>
}</div>
                    <div class="description">
                        خطا در انتقال کد ووچر - کد نامعتبر یا قبلاً استفاده شده است.
                    </div>
                </div>
            </div>
        </div>

        <div class="api-section">
            <div class="api-title">
                <i class="fas fa-wallet"></i> نحوه انتقال کد ووچر به کیف پول شما
            </div>
            <div class="api-url">
                <button class="copy-btn" onclick="copyToClipboard(this)">کپی</button>
                https://speedx-team.ir/BIT/api?token=TOKEN&bv-code=CODE&action=wallet
            </div>
            <div class="description">
                <strong>پارامترها:</strong><br>
                • <code>token</code>: توکن شما از بخش "👀 مشاهده توکن"<br>
                • <code>bv-code</code>: کد ووچر مورد نظر<br>
                • <code>action</code>: wallet (انتقال به کیف پول)
            </div>

            <div class="response-container">
                <div class="response-box">
                    <div class="response-title success">✅ انتقال موفق به کیف پول</div>
                    <div class="json-code">{
  <span class="json-key">"status"</span>: <span class="json-string">"success"</span>,
  <span class="json-key">"message"</span>: <span class="json-string">"کد ووچر با موفقیت به کیف پول اضافه شد"</span>,
  <span class="json-key">"amount"</span>: <span class="json-number">20000</span>,
  <span class="json-key">"wallet_balance"</span>: <span class="json-number">65000</span>
}</div>
                    <div class="description">
                        کد ووچر با موفقیت به کیف پول شما اضافه شد و موجودی جدید کیف پول نمایش داده شده است.
                    </div>
                </div>

                <div class="response-box">
                    <div class="response-title error">❌ خطا در انتقال</div>
                    <div class="json-code">{
  <span class="json-key">"status"</span>: <span class="json-string">"error"</span>,
  <span class="json-key">"message"</span>: <span class="json-string">"کد ووچر نامعتبر یا استفاده شده است"</span>
}</div>
                    <div class="description">
                        خطا در انتقال کد ووچر به کیف پول - کد نامعتبر یا قبلاً استفاده شده است.
                    </div>
                </div>
            </div>
        </div>



        <div class="footer">
            <p>© 2025 BitVoucher API - تمامی حقوق محفوظ است</p>
        </div>
    </div>

    <script>
        function copyToClipboard(button) {
            const text = button.parentElement.textContent.replace('کپی', '').trim();
            navigator.clipboard.writeText(text).then(() => {
                const originalText = button.textContent;
                button.textContent = '✓ کپی شد';
                button.style.background = 'rgba(72, 187, 120, 0.3)';
                button.style.color = '#68d391';
                
                setTimeout(() => {
                    button.textContent = originalText;
                    button.style.background = 'rgba(0, 212, 255, 0.2)';
                    button.style.color = '#00d4ff';
                }, 2000);
            });
        }

        // Add smooth scroll animation
        document.addEventListener('DOMContentLoaded', function() {
            const responseBoxes = document.querySelectorAll('.response-box');
            
            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        entry.target.style.opacity = '1';
                        entry.target.style.transform = 'translateY(0)';
                    }
                });
            });

            responseBoxes.forEach(box => {
                box.style.opacity = '0';
                box.style.transform = 'translateY(20px)';
                box.style.transition = 'all 0.6s ease';
                observer.observe(box);
            });
        });

        // Add typing animation to JSON
        function typeJSON(element, json, speed = 50) {
            element.innerHTML = '';
            let i = 0;
            
            function typeWriter() {
                if (i < json.length) {
                    element.innerHTML += json.charAt(i);
                    i++;
                    setTimeout(typeWriter, speed);
                }
            }
            
            typeWriter();
        }
    </script>
</body>
</html>
